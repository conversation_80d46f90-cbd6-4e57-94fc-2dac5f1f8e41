import nodemailer from 'nodemailer';
import { EmailConfig } from '../models/EmailConfig.js';

/**
 * 邮件服务工具类
 */
export class EmailService {
  constructor() {
    this.transporter = null;
    this.config = null;
  }

  /**
   * 初始化邮件传输器
   * @returns {Promise<boolean>} 初始化是否成功
   */
  async initialize() {
    try {
      // 获取激活的邮箱配置
      this.config = await EmailConfig.getActiveConfig();
      
      if (!this.config) {
        console.warn('未找到激活的邮箱配置');
        return false;
      }

      // 创建传输器
      const transportConfig = {
        host: this.config.smtp_host,
        port: this.config.smtp_port,
        secure: this.config.smtp_secure,
        auth: {
          user: this.config.smtp_user,
          pass: this.config.smtp_pass
        }
      };

      // 根据不同邮箱服务商设置特殊配置
      if (this.config.smtp_host.includes('gmail.com')) {
        // Gmail 特殊配置
        transportConfig.secure = false; // 使用 STARTTLS
        transportConfig.requireTLS = true;
        transportConfig.tls = {
          ciphers: 'SSLv3'
        };
      } else {
        // 其他邮箱（如163）的配置
        transportConfig.tls = {
          rejectUnauthorized: false
        };
      }

      this.transporter = nodemailer.createTransport(transportConfig);

      // 验证连接
      await this.transporter.verify();
      console.log('邮件服务初始化成功');
      return true;
    } catch (error) {
      console.error('邮件服务初始化失败:', error);
      this.transporter = null;
      this.config = null;
      return false;
    }
  }

  /**
   * 发送邮件
   * @param {Object} mailOptions - 邮件选项
   * @returns {Promise<Object>} 发送结果
   */
  async sendMail(mailOptions) {
    try {
      // 确保传输器已初始化
      if (!this.transporter) {
        const initialized = await this.initialize();
        if (!initialized) {
          throw new Error('邮件服务未初始化');
        }
      }

      // 设置默认发件人信息
      const options = {
        from: `"${this.config.from_name}" <${this.config.from_email}>`,
        ...mailOptions
      };

      const result = await this.transporter.sendMail(options);
      console.log('邮件发送成功:', result.messageId);
      return result;
    } catch (error) {
      console.error('邮件发送失败:', error);
      throw error;
    }
  }

  /**
   * 发送验证码邮件
   * @param {string} email - 收件人邮箱
   * @param {string} code - 验证码
   * @param {string} type - 验证码类型
   * @returns {Promise<Object>} 发送结果
   */
  async sendVerificationCode(email, code, type = 'register') {
    const templates = {
      register: {
        subject: '【走失寵物協尋平台】郵箱驗證碼',
        html: this.getRegisterTemplate(code)
      },
      reset_password: {
        subject: '【走失寵物協尋平台】密碼重置驗證碼',
        html: this.getResetPasswordTemplate(code)
      }
    };

    const template = templates[type];
    if (!template) {
      throw new Error('不支持的驗證碼類型');
    }

    return await this.sendMail({
      to: email,
      subject: template.subject,
      html: template.html
    });
  }

  /**
   * 获取注册验证码邮件模板
   * @param {string} code - 验证码
   * @returns {string} HTML模板
   */
  getRegisterTemplate(code) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>郵箱驗證碼</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #4F46E5; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
          .code { background: #fff; border: 2px dashed #4F46E5; padding: 20px; text-align: center; margin: 20px 0; border-radius: 8px; }
          .code-number { font-size: 32px; font-weight: bold; color: #4F46E5; letter-spacing: 5px; }
          .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
          .warning { background: #FEF3C7; border-left: 4px solid #F59E0B; padding: 15px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>走失寵物協尋平台</h1>
            <p>郵箱驗證碼</p>
          </div>
          <div class="content">
            <h2>您好！</h2>
            <p>感謝您註冊走失寵物協尋平台。為了確保您的賬戶安全，請使用以下驗證碼完成郵箱驗證：</p>

            <div class="code">
              <div class="code-number">${code}</div>
              <p>驗證碼有效期：10分鐘</p>
            </div>

            <div class="warning">
              <strong>安全提醒：</strong>
              <ul>
                <li>請勿將驗證碼告訴他人</li>
                <li>驗證碼僅用於本次註冊，請在10分鐘內使用</li>
                <li>如果您沒有進行註冊操作，請忽略此郵件</li>
              </ul>
            </div>

            <p>如果您有任何問題，請聯繫我們的客服團隊。</p>
            <p>祝您使用愉快！</p>
          </div>
          <div class="footer">
            <p>此郵件由系統自動發送，請勿回復</p>
            <p>© 2025 走失寵物協尋平台 版權所有</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * 获取密码重置验证码邮件模板
   * @param {string} code - 验证码
   * @returns {string} HTML模板
   */
  getResetPasswordTemplate(code) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>密碼重置驗證碼</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #DC2626; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
          .code { background: #fff; border: 2px dashed #DC2626; padding: 20px; text-align: center; margin: 20px 0; border-radius: 8px; }
          .code-number { font-size: 32px; font-weight: bold; color: #DC2626; letter-spacing: 5px; }
          .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
          .warning { background: #FEE2E2; border-left: 4px solid #DC2626; padding: 15px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>走失寵物協尋平台</h1>
            <p>密碼重置驗證碼</p>
          </div>
          <div class="content">
            <h2>密碼重置請求</h2>
            <p>我們收到了您的密碼重置請求。請使用以下驗證碼完成密碼重置：</p>

            <div class="code">
              <div class="code-number">${code}</div>
              <p>驗證碼有效期：10分鐘</p>
            </div>

            <div class="warning">
              <strong>安全提醒：</strong>
              <ul>
                <li>如果您沒有申請密碼重置，請立即聯繫我們</li>
                <li>請勿將驗證碼告訴他人</li>
                <li>驗證碼僅用於本次密碼重置，請在10分鐘內使用</li>
              </ul>
            </div>

            <p>如果您有任何問題，請聯繫我們的客服團隊。</p>
          </div>
          <div class="footer">
            <p>此郵件由系統自動發送，請勿回復</p>
            <p>© 2024 走失寵物協尋平台 版權所有</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * 测试邮件配置
   * @param {Object} configData - 邮箱配置数据
   * @returns {Promise<boolean>} 测试是否成功
   */
  static async testConfig(configData) {
    try {
      const transportConfig = {
        host: configData.smtp_host,
        port: configData.smtp_port,
        secure: configData.smtp_secure,
        auth: {
          user: configData.smtp_user,
          pass: configData.smtp_pass
        }
      };

      // 根据不同邮箱服务商设置特殊配置
      if (configData.smtp_host.includes('gmail.com')) {
        // Gmail 特殊配置
        transportConfig.secure = false; // 使用 STARTTLS
        transportConfig.requireTLS = true;
        transportConfig.tls = {
          ciphers: 'SSLv3'
        };
      } else {
        // 其他邮箱（如163）的配置
        transportConfig.tls = {
          rejectUnauthorized: false
        };
      }

      const testTransporter = nodemailer.createTransport(transportConfig);

      await testTransporter.verify();
      return true;
    } catch (error) {
      console.error('邮箱配置测试失败:', error);
      return false;
    }
  }

  /**
   * 发送测试邮件
   * @param {string} email - 收件人邮箱
   * @returns {Promise<Object>} 发送结果
   */
  async sendTestEmail(email) {
    const html = `
      <h2>郵箱配置測試</h2>
      <p>這是一封測試郵件，用於驗證郵箱配置是否正確。</p>
      <p>如果您收到此郵件，說明郵箱配置已成功！</p>
      <p>發送時間：${new Date().toLocaleString('zh-CN')}</p>
    `;

    return await this.sendMail({
      to: email,
      subject: '【走失寵物協尋平台】郵箱配置測試',
      html
    });
  }
}

// 创建全局邮件服务实例
export const emailService = new EmailService();
